import subprocess
import re
import platform
import socket
import time
from typing import List, Dict, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich import box

# Try to import scapy for Python-based traceroute
try:
    from scapy.all import IP, ICMP, UDP, sr1
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False

class Traceroute:
    """Traceroute module for NetworkScan Pro."""

    def __init__(self, console: Console):
        """Initialize traceroute with the console for output."""
        self.console = console
        self.use_system_traceroute = self._check_system_traceroute_available()

    def _check_system_traceroute_available(self) -> bool:
        """Check if system traceroute command is available."""
        try:
            platform_name = platform.system()
            if platform_name == "Windows":
                # Test if tracert command exists and works
                result = subprocess.run(["tracert", "-h", "1", "127.0.0.1"],
                                      capture_output=True, timeout=10, text=True)
                # Check if the command actually produced traceroute output
                return "Tracing route" in result.stdout or result.returncode == 0
            else:
                # Test if traceroute command exists and works
                result = subprocess.run(["traceroute", "-m", "1", "127.0.0.1"],
                                      capture_output=True, timeout=10, text=True)
                # Check if the command actually produced traceroute output
                return "traceroute to" in result.stdout or result.returncode == 0
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired, OSError):
            return False
        
    def _parse_windows_tracert(self, output: str) -> List[Dict[str, Any]]:
        """
        Parse the output of Windows tracert command.
        
        Args:
            output: Output string from tracert command
            
        Returns:
            List of dictionaries with hop information
        """
        hops = []
        
        # Split the output into lines
        lines = output.strip().split('\n')
        
        # Skip the first few lines (header)
        hop_lines = [line for line in lines if re.match(r'^\s*\d+', line)]
        
        for line in hop_lines:
            # Extract hop number
            hop_match = re.match(r'^\s*(\d+)', line)
            if not hop_match:
                continue
                
            hop_num = int(hop_match.group(1))
            
            # Extract RTTs
            rtts = re.findall(r'(\d+) ms', line)
            rtts = [int(rtt) for rtt in rtts]
            
            # Extract hostname/IP
            ip_match = re.search(r'\[([\d\.]+)\]', line)
            hostname_match = re.search(r'ms\s+([^\[]+)(?:\s+\[|$)', line)
            
            ip = ip_match.group(1) if ip_match else "*"
            hostname = hostname_match.group(1).strip() if hostname_match else "*"
            
            # Handle timeouts
            if "Request timed out" in line or "*" in line:
                ip = "*"
                hostname = "*"
                rtts = []
                
            hop = {
                "hop": hop_num,
                "ip": ip,
                "hostname": hostname,
                "rtts": rtts,
                "avg_rtt": sum(rtts) / len(rtts) if rtts else None,
                "asn": None,
                "isp": None
            }
            
            hops.append(hop)
            
        return hops
        
    def _parse_linux_traceroute(self, output: str) -> List[Dict[str, Any]]:
        """
        Parse the output of Linux/Mac traceroute command.
        
        Args:
            output: Output string from traceroute command
            
        Returns:
            List of dictionaries with hop information
        """
        hops = []
        
        # Split the output into lines
        lines = output.strip().split('\n')
        
        # Skip the first line (header)
        hop_lines = lines[1:]
        
        for line in hop_lines:
            # Extract hop number
            hop_match = re.match(r'^\s*(\d+)', line)
            if not hop_match:
                continue
                
            hop_num = int(hop_match.group(1))
            
            # Extract hostname/IP
            name_ip_match = re.search(r'^\s*\d+\s+([^\s]+)\s+\(([\d\.]+)\)', line)
            
            if name_ip_match:
                hostname = name_ip_match.group(1)
                ip = name_ip_match.group(2)
            else:
                # Check for timeout
                if "*" in line:
                    hostname = "*"
                    ip = "*"
                else:
                    # Just IP, no hostname
                    ip_match = re.search(r'^\s*\d+\s+\(([\d\.]+)\)', line)
                    if ip_match:
                        ip = ip_match.group(1)
                        hostname = ip
                    else:
                        # Could not parse, skip this line
                        continue
                        
            # Extract RTTs
            rtts = re.findall(r'([\d\.]+) ms', line)
            rtts = [float(rtt) for rtt in rtts]
            
            hop = {
                "hop": hop_num,
                "ip": ip,
                "hostname": hostname,
                "rtts": rtts,
                "avg_rtt": sum(rtts) / len(rtts) if rtts else None,
                "asn": None,
                "isp": None
            }
            
            hops.append(hop)
            
        return hops
        
    def _get_asn_info(self, ip: str) -> Dict[str, Optional[str]]:
        """
        Try to get ASN information for an IP address.
        
        Args:
            ip: IP address
            
        Returns:
            Dictionary with ASN and ISP information
        """
        if ip == "*":
            return {"asn": None, "isp": None}
            
        # This is a simplified version. In a real implementation,
        # you would use a service like Team Cymru IP-to-ASN service, or
        # a local database like MaxMind GeoIP.
        # For simplicity, we'll just return placeholder values.
        
        return {
            "asn": "AS00000",
            "isp": "Unknown ISP"
        }
        
    def trace(self, target: str, max_hops: int = 30):
        """
        Perform a traceroute to a target.

        Args:
            target: Target IP address or hostname
            max_hops: Maximum number of hops
        """
        # Try to resolve the target to display IP
        try:
            ip = socket.gethostbyname(target)
            self.console.print(f"[bold]Tracing route to [yellow]{target} ({ip})[/yellow][/bold]")
        except socket.gaierror:
            self.console.print(f"[bold]Tracing route to [yellow]{target}[/yellow][/bold]")
            ip = target

        # Choose implementation based on availability
        if self.use_system_traceroute:
            self.console.print("[dim]Using system traceroute...[/dim]")
            hops = self._trace_system(target, max_hops)
        elif SCAPY_AVAILABLE:
            self.console.print("[dim]Using Python/scapy traceroute...[/dim]")
            hops = self._trace_python(ip, max_hops)
        else:
            self.console.print("[bold red]Error: No traceroute implementation available[/bold red]")
            return

        if hops:
            # Try to get ASN information for each hop
            try:
                for hop in hops:
                    if hop["ip"] != "*":
                        asn_info = self._get_asn_info(hop["ip"])
                        hop["asn"] = asn_info["asn"]
                        hop["isp"] = asn_info["isp"]
            except Exception as e:
                self.console.print(f"[yellow]Warning: Could not get ASN info: {str(e)}[/yellow]")
                # Continue without ASN info
                for hop in hops:
                    if "asn" not in hop:
                        hop["asn"] = "AS00000"
                        hop["isp"] = "Unknown ISP"

            # Display results
            try:
                self._display_results(target, hops)
            except Exception as e:
                self.console.print(f"[bold red]Error displaying results: {str(e)}[/bold red]")
        else:
            self.console.print(f"[bold red]Error: Failed to perform traceroute to {target}[/bold red]")

    def _trace_system(self, target: str, max_hops: int) -> List[Dict[str, Any]]:
        """Perform traceroute using system commands."""
        platform_name = platform.system()

        try:
            # Create progress spinner
            with Progress(
                SpinnerColumn(),
                TextColumn("[bold blue]Tracing route to [yellow]{task.fields[target]}[/yellow]..."),
                console=self.console
            ) as progress:
                # Create task for progress tracking
                task_id = progress.add_task("Tracing...", target=target)

                if platform_name == "Windows":
                    # On Windows, use tracert
                    cmd = ["powershell", "-Command", f"tracert -d -h {max_hops} {target}"]
                    output = subprocess.check_output(cmd, text=True)
                    return self._parse_windows_tracert(output)
                else:
                    # On Linux/Mac, use traceroute
                    cmd = ["traceroute", "-n", "-m", str(max_hops), target]
                    output = subprocess.check_output(cmd, text=True)
                    return self._parse_linux_traceroute(output)

        except subprocess.CalledProcessError as e:
            self.console.print(f"[yellow]System traceroute failed: {str(e)}[/yellow]")
            return []
        except Exception as e:
            self.console.print(f"[yellow]System traceroute error: {str(e)}[/yellow]")
            return []

    def _trace_python(self, target_ip: str, max_hops: int) -> List[Dict[str, Any]]:
        """Perform traceroute using Python/scapy implementation."""
        hops = []

        try:
            # Create progress spinner
            with Progress(
                SpinnerColumn(),
                TextColumn("[bold blue]Tracing route to [yellow]{task.fields[target]}[/yellow]..."),
                console=self.console
            ) as progress:
                # Create task for progress tracking
                task_id = progress.add_task("Tracing...", target=target_ip)

                for ttl in range(1, min(max_hops + 1, 6)):  # Limit to 5 hops for simulation
                    # Create ICMP packet with specific TTL
                    packet = IP(dst=target_ip, ttl=ttl) / ICMP()

                    # Send packet and wait for response
                    start_time = time.time()
                    reply = sr1(packet, verbose=0, timeout=3)
                    end_time = time.time()

                    if reply is None:
                        # No response - timeout
                        hop = {
                            "hop": ttl,
                            "ip": "*",
                            "hostname": "*",
                            "rtts": [],
                            "avg_rtt": None,
                            "asn": None,
                            "isp": None
                        }
                    else:
                        # Got a response
                        rtt = (end_time - start_time) * 1000  # Convert to ms
                        hop_ip = reply.src

                        hop = {
                            "hop": ttl,
                            "ip": hop_ip,
                            "hostname": hop_ip,  # We'll use IP as hostname for simplicity
                            "rtts": [rtt],
                            "avg_rtt": rtt,
                            "asn": None,
                            "isp": None
                        }

                        # Check if we've reached the destination
                        if hop_ip == target_ip:
                            hops.append(hop)
                            break

                    hops.append(hop)

        except PermissionError:
            # Scapy requires root privileges, simulate a traceroute
            self.console.print("[yellow]Note: scapy requires root privileges. Using simulated traceroute.[/yellow]")
            return self._simulate_traceroute(target_ip, max_hops)
        except Exception as e:
            self.console.print(f"[yellow]Python traceroute error: {str(e)}. Using simulated traceroute.[/yellow]")
            return self._simulate_traceroute(target_ip, max_hops)

        return hops

    def _simulate_traceroute(self, target_ip: str, max_hops: int) -> List[Dict[str, Any]]:
        """Simulate a traceroute for testing purposes."""
        hops = []

        # Create a few simulated hops
        simulated_ips = [
            "***********",
            "********",
            "**********",
            target_ip
        ]

        for i, ip in enumerate(simulated_ips[:min(max_hops, 4)], 1):
            hop = {
                "hop": i,
                "ip": ip,
                "hostname": ip,
                "rtts": [20.0 + i * 10],
                "avg_rtt": 20.0 + i * 10,
                "asn": None,
                "isp": None
            }
            hops.append(hop)

            if ip == target_ip:
                break

        return hops
            
    def _display_results(self, target: str, hops: List[Dict[str, Any]]):
        """
        Display traceroute results in a formatted table.
        
        Args:
            target: Target that was traced
            hops: List of dictionaries with hop information
        """
        # Create descriptive panels first
        info_panel = Panel(
            "[dim]• [blue]Route visualization[/blue] from your computer to the target\n"
            "• [yellow]Asterisks (*)[/yellow] in IP column indicate routers that don't respond to ICMP\n"
            "• [yellow]Asterisks (*)[/yellow] in ASN/ISP columns are placeholders (data not available)\n"
            "• [magenta]RTT (Round Trip Time)[/magenta] shows latency at each hop[/dim]",
            title="[bold]About Traceroute[/bold]",
            border_style="blue",
            padding=(1, 2)
        )
        self.console.print(info_panel)
        
        # Create results table with improved styling
        table = Table(
            title=f"Traceroute Results for {target}",
            box=box.ROUNDED,
            title_style="bold cyan",
            border_style="blue",
            header_style="bold cyan"
        )
        table.add_column("Hop", style="cyan", justify="right")
        table.add_column("IP Address", style="bright_yellow")
        table.add_column("Hostname", style="green")
        table.add_column("Avg RTT (ms)", style="magenta", justify="right")
        table.add_column("ASN", style="blue")
        table.add_column("ISP", style="white")
        
        for hop in hops:
            # Format values for display
            hop_num = str(hop["hop"])
            ip = hop["ip"]
            hostname = hop["hostname"]
            avg_rtt = f"{hop['avg_rtt']:.2f}" if hop["avg_rtt"] is not None else "—"
            asn = hop["asn"] or "—"
            isp = hop["isp"] or "—"
            
            # Use appropriate colors for timeouts
            if ip == "*":
                ip_display = f"[dim red]*[/dim red]"
                hostname_display = f"[dim red]*[/dim red]"
                avg_rtt_display = f"[dim red]—[/dim red]"
            else:
                # Calculate color for RTT value (green for fast, yellow for medium, red for slow)
                rtt_color = "green"
                if hop["avg_rtt"] is not None:
                    if hop["avg_rtt"] > 100:
                        rtt_color = "red"
                    elif hop["avg_rtt"] > 50:
                        rtt_color = "yellow"
                        
                ip_display = ip
                hostname_display = hostname
                avg_rtt_display = f"[{rtt_color}]{avg_rtt}[/{rtt_color}]"
                
            table.add_row(
                hop_num,
                ip_display,
                hostname_display,
                avg_rtt_display,
                asn,
                isp
            )
            
        self.console.print(table) 