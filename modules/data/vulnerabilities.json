{"OpenSSH": [{"versions": ["7.2p1", "7.2p2"], "cve": "CVE-2016-6210", "description": "User enumeration via timing attack", "severity": "Medium", "fixed_in": "7.3p1"}, {"versions": ["<7.4"], "cve": "CVE-2016-10009", "description": "Privilege escalation via agent forwarding", "severity": "High", "fixed_in": "7.4"}], "Apache": [{"versions": ["2.4.0", "2.4.1", "2.4.2", "2.4.3", "2.4.4", "2.4.5", "2.4.6", "2.4.7", "2.4.8", "2.4.9"], "cve": "CVE-2014-0226", "description": "Race condition in mod_status", "severity": "High", "fixed_in": "2.4.10"}, {"versions": ["2.4.0", "2.4.1", "2.4.2", "2.4.3", "2.4.4", "2.4.5", "2.4.6", "2.4.7", "2.4.8", "2.4.9", "2.4.10", "2.4.11", "2.4.12", "2.4.13", "2.4.14", "2.4.15", "2.4.16"], "cve": "CVE-2016-5387", "description": "HTTP header injection via HTTP_PROXY", "severity": "Medium", "fixed_in": "2.4.17"}], "Nginx": [{"versions": ["<1.5.11"], "cve": "CVE-2014-0133", "description": "SPDY heap buffer overflow", "severity": "High", "fixed_in": "1.5.11"}], "IIS": [{"versions": ["7.5"], "cve": "CVE-2010-3972", "description": "FTP service stack overflow", "severity": "High", "fixed_in": "Patch MS11-004"}], "MySQL": [{"versions": ["<5.7.19"], "cve": "CVE-2017-3636", "description": "Privilege escalation vulnerability", "severity": "High", "fixed_in": "5.7.19"}], "MariaDB": [{"versions": ["<10.2.8"], "cve": "CVE-2017-3636", "description": "Privilege escalation vulnerability", "severity": "High", "fixed_in": "10.2.8"}], "vsFTPd": [{"versions": ["2.3.4"], "cve": "CVE-2011-2523", "description": "Backdoor vulnerability", "severity": "Critical", "fixed_in": "2.3.5"}]}