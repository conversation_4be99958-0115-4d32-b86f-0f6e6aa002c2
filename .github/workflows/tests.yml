name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        exclude:
          # Reduce matrix size for faster CI
          - os: windows-latest
            python-version: '3.8'
          - os: macos-latest
            python-version: '3.8'

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run comprehensive tests
      run: |
        python test_all_features.py
    
    - name: Test CLI help command
      run: |
        python networkscanner.py --help
    
    - name: Test basic port scan (non-privileged)
      run: |
        python networkscanner.py scan 127.0.0.1 --ports 80,443 --timeout 1
    
    - name: Test DNS lookup
      run: |
        python networkscanner.py dns google.com --type a
    
    - name: Test network info
      run: |
        python networkscanner.py netinfo --type local

  security-audit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install safety bandit
    
    - name: Run safety check
      run: |
        safety check --json || true
    
    - name: Run bandit security check
      run: |
        bandit -r . -f json || true

  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort
    
    - name: Check code formatting with black
      run: |
        black --check --diff .
      continue-on-error: true
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
      continue-on-error: true
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      continue-on-error: true
