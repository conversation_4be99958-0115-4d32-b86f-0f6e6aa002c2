---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Run command '...'
2. Enter parameters '...'
3. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots/Output**
If applicable, add screenshots or copy the terminal output to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Windows 10, Ubuntu 20.04, macOS 12]
 - Python Version: [e.g. 3.9.7]
 - CLI Network Scanner Version: [e.g. 1.2.1]

**Additional context**
Add any other context about the problem here.

**Error Messages**
```
Paste any error messages here
```

**Command Used**
```bash
# Paste the exact command that caused the issue
python networkscanner.py ...
```
